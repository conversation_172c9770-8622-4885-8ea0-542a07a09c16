using System.ComponentModel.DataAnnotations;

namespace AICreator.Models
{
    public class Story
    {
        public Guid Id { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }

        // Krok 1: Podstawowe informacje
        [MaxLength(100)]
        public string? Genre { get; set; }

        [MaxLength(1000)]
        public string? Characters { get; set; }

        [MaxLength(2000)]
        public string? PlotOutline { get; set; }

        // Krok 2: Wygenerowany konspekt
        public string? GeneratedOutline { get; set; }

        // Wygenerowana treść
        public string Content { get; set; } = string.Empty;

        public bool IsCompleted { get; set; } = false;
    }
}
