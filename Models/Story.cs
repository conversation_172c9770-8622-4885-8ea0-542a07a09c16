using System.ComponentModel.DataAnnotations;

namespace AICreator.Models
{
    public class Story
    {
        public Guid Id { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;
        
        public string Content { get; set; } = string.Empty;
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime UpdatedAt { get; set; }
        
        [MaxLength(500)]
        public string? Description { get; set; }
        
        public bool IsCompleted { get; set; } = false;
    }
}
