using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace AICreator.Models
{
    public class Story
    {
        public Guid Id { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }

        // Krok 1: Podstawowe informacje
        [MaxLength(100)]
        public string? Genre { get; set; }

        [MaxLength(1000)]
        public string? Characters { get; set; }

        [MaxLength(2000)]
        public string? PlotOutline { get; set; }

        // Krok 2: Wygenerowany konspekt (JSON)
        public string? GeneratedOutlineJson { get; set; }

        // Helper property dla łatwego dostępu do punktów konspektu
        [NotMapped]
        public List<string> OutlinePoints
        {
            get
            {
                if (string.IsNullOrEmpty(GeneratedOutlineJson))
                    return new List<string>();

                try
                {
                    return JsonSerializer.Deserialize<List<string>>(GeneratedOutlineJson) ?? new List<string>();
                }
                catch
                {
                    return new List<string>();
                }
            }
            set
            {
                GeneratedOutlineJson = JsonSerializer.Serialize(value);
            }
        }

        // Wygenerowana treść
        public string Content { get; set; } = string.Empty;

        public bool IsCompleted { get; set; } = false;
    }
}
