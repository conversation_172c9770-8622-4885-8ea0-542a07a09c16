html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
  font-family: 'Inter', sans-serif;
}

/* Home page specific styles */
.home-body {
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow-x: hidden;
}

.home-main {
  height: 100vh;
  padding: 0;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Hero Section Styles */
.hero-container {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.hero-content {
  text-align: center;
  z-index: 10;
  position: relative;
  max-width: 600px;
  padding: 2rem;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 3rem;
  font-weight: 300;
  animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-action {
  animation: fadeInUp 1s ease-out 0.4s both;
}

.btn-new-story {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2.5rem;
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  color: white;
  text-decoration: none;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 600;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-family: 'Inter', sans-serif;
}

.hero-action form {
  display: inline-block;
}

.btn-new-story:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
  color: white;
  text-decoration: none;
}

.btn-new-story:active {
  transform: translateY(-1px);
}

.btn-icon {
  font-size: 1.5rem;
  animation: sparkle 2s ease-in-out infinite;
}

/* Floating Background Elements */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.element-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.element-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.element-3 {
  width: 60px;
  height: 60px;
  top: 80%;
  left: 20%;
  animation-delay: 4s;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.2) rotate(180deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .btn-new-story {
    padding: 0.875rem 2rem;
    font-size: 1.1rem;
  }

  .hero-content {
    padding: 1rem;
  }
}

/* Story Creator Styles */
.card {
  border: none;
  border-radius: 15px;
}

.card-header {
  border-radius: 15px 15px 0 0 !important;
  padding: 1.5rem;
}

.form-control {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control-lg {
  padding: 0.75rem 1rem;
  font-size: 1.1rem;
}

.btn {
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(45deg, #007bff, #0056b3);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(45deg, #0056b3, #004085);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.badge {
  font-size: 0.9rem;
  padding: 0.5rem 0.75rem;
}

/* Step 2 Styles */
.outline-container {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 1.5rem;
  border: 2px solid #e9ecef;
}

.generated-outline {
  font-family: 'Georgia', serif;
  line-height: 1.8;
}

.outline-item {
  padding: 0.75rem 0;
  border-bottom: 1px solid #dee2e6;
  font-size: 1.05rem;
}

.outline-item:last-child {
  border-bottom: none;
}

.outline-item:hover {
  background-color: #ffffff;
  border-radius: 5px;
  padding-left: 1rem;
  transition: all 0.3s ease;
}

.card.bg-light .card-body {
  padding: 1rem;
}

.card.bg-light .card-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.card.bg-light .card-text {
  font-size: 0.85rem;
  color: #6c757d;
  margin-bottom: 0;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Editable Outline Styles */
.outline-item-container {
  position: relative;
  margin-bottom: 0.5rem;
}

.outline-item-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.outline-item-wrapper:hover {
  background-color: #f8f9fa;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.outline-item-controls {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.outline-item-wrapper:hover .outline-item-controls {
  opacity: 1;
}

.outline-item-controls .btn {
  width: 28px;
  height: 28px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.outline-item-content {
  flex: 1;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.item-number {
  font-weight: 600;
  color: #007bff;
  min-width: 30px;
  margin-top: 0.5rem;
}

.outline-item-text {
  flex: 1;
  border: 2px solid transparent;
  background: transparent;
  font-family: 'Georgia', serif;
  font-size: 1.05rem;
  line-height: 1.6;
  padding: 0.5rem;
  border-radius: 6px;
  resize: none;
  overflow: hidden;
  transition: all 0.3s ease;
}

.outline-item-text:focus {
  outline: none;
  border-color: #007bff;
  background-color: #ffffff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.outline-item-text:hover {
  border-color: #dee2e6;
  background-color: #ffffff;
}

.add-item-bottom {
  text-align: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 2px dashed #dee2e6;
}

.add-item-btn, .delete-item-btn {
  transition: all 0.3s ease;
}

.add-item-btn:hover {
  background-color: #28a745;
  color: white;
  transform: scale(1.1);
}

.delete-item-btn:hover {
  background-color: #dc3545;
  color: white;
  transform: scale(1.1);
}