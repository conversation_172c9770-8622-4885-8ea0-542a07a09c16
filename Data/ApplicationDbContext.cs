using Microsoft.EntityFrameworkCore;
using AICreator.Models;

namespace AICreator.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<Story> Stories { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<Story>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.Genre).HasMaxLength(100);
                entity.Property(e => e.Characters).HasMaxLength(1000);
                entity.Property(e => e.PlotOutline).HasMaxLength(2000);
                entity.Property(e => e.GeneratedOutline).HasColumnType("TEXT");
                entity.Property(e => e.Content).HasColumnType("TEXT");
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.UpdatedAt).IsRequired();
            });
        }
    }
}
