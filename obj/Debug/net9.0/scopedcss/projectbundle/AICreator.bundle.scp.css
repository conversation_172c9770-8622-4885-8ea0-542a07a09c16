/* _content/AICreator/Pages/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-e4wy1uw31v] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-e4wy1uw31v] {
  color: #0077cc;
}

.btn-primary[b-e4wy1uw31v] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-e4wy1uw31v], .nav-pills .show > .nav-link[b-e4wy1uw31v] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-e4wy1uw31v] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-e4wy1uw31v] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-e4wy1uw31v] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-e4wy1uw31v] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-e4wy1uw31v] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
