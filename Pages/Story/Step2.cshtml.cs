using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using AICreator.Data;
using AICreator.Models;
using AICreator.Services;
using System.Text.Json;

namespace AICreator.Pages.Story
{
    public class Step2Model : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly OpenRouterService _openRouterService;

        public Step2Model(ApplicationDbContext context, OpenRouterService openRouterService)
        {
            _context = context;
            _openRouterService = openRouterService;
        }

        [BindProperty]
        public Models.Story Story { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(Guid id)
        {
            var story = await _context.Stories
                .Select(s => new Models.Story
                {
                    Id = s.Id,
                    Genre = s.Genre,
                    Characters = s.Characters,
                    PlotOutline = s.PlotOutline,
                    GeneratedOutline = s.GeneratedOutline,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.UpdatedAt,
                    Content = s.Content,
                    IsCompleted = s.IsCompleted
                })
                .FirstOrDefaultAsync(m => m.Id == id);

            if (story == null)
            {
                return NotFound();
            }

            Story = story;
            return Page();
        }

        public async Task<IActionResult> OnPostRegenerateOutlineAsync()
        {
            var storyToUpdate = await _context.Stories.FindAsync(Story.Id);
            if (storyToUpdate == null)
            {
                return NotFound();
            }

            try
            {
                var outline = await _openRouterService.GenerateStoryOutlineAsync(
                    storyToUpdate.Genre ?? "",
                    storyToUpdate.Characters ?? "",
                    storyToUpdate.PlotOutline ?? "");

                storyToUpdate.GeneratedOutline = outline;
                storyToUpdate.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                storyToUpdate.GeneratedOutline = $"Błąd generowania konspektu: {ex.Message}";
                await _context.SaveChangesAsync();
            }

            return RedirectToPage("./Step2", new { id = Story.Id });
        }

        public async Task<IActionResult> OnPostUpdateItemAsync()
        {
            using var reader = new StreamReader(Request.Body);
            var body = await reader.ReadToEndAsync();
            var data = JsonSerializer.Deserialize<UpdateItemRequest>(body);

            if (data == null) return BadRequest();

            var story = await _context.Stories.FindAsync(Story.Id);
            if (story == null) return NotFound();

            var lines = story.GeneratedOutline?.Split('\n', StringSplitOptions.RemoveEmptyEntries).ToList() ?? new List<string>();

            if (data.Index >= 0 && data.Index < lines.Count)
            {
                lines[data.Index] = data.Content;
                story.GeneratedOutline = string.Join('\n', lines);
                story.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }

            return new JsonResult(new { success = true });
        }

        public async Task<IActionResult> OnPostAddItemAsync()
        {
            using var reader = new StreamReader(Request.Body);
            var body = await reader.ReadToEndAsync();
            var data = JsonSerializer.Deserialize<AddItemRequest>(body);

            if (data == null) return BadRequest();

            var story = await _context.Stories.FindAsync(Story.Id);
            if (story == null) return NotFound();

            var lines = story.GeneratedOutline?.Split('\n', StringSplitOptions.RemoveEmptyEntries).ToList() ?? new List<string>();

            if (data.Index >= 0 && data.Index <= lines.Count)
            {
                lines.Insert(data.Index, data.Content);
                story.GeneratedOutline = string.Join('\n', lines);
                story.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }

            return new JsonResult(new { success = true });
        }

        public async Task<IActionResult> OnPostDeleteItemAsync()
        {
            using var reader = new StreamReader(Request.Body);
            var body = await reader.ReadToEndAsync();
            var data = JsonSerializer.Deserialize<DeleteItemRequest>(body);

            if (data == null) return BadRequest();

            var story = await _context.Stories.FindAsync(Story.Id);
            if (story == null) return NotFound();

            var lines = story.GeneratedOutline?.Split('\n', StringSplitOptions.RemoveEmptyEntries).ToList() ?? new List<string>();

            if (data.Index >= 0 && data.Index < lines.Count)
            {
                lines.RemoveAt(data.Index);
                story.GeneratedOutline = string.Join('\n', lines);
                story.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }

            return new JsonResult(new { success = true });
        }
    }

    public class UpdateItemRequest
    {
        public int Index { get; set; }
        public string Content { get; set; } = string.Empty;
    }

    public class AddItemRequest
    {
        public int Index { get; set; }
        public string Content { get; set; } = string.Empty;
    }

    public class DeleteItemRequest
    {
        public int Index { get; set; }
    }
}
