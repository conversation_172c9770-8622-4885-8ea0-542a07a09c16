using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using AICreator.Data;
using AICreator.Models;

namespace AICreator.Pages.Story
{
    public class EditModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public EditModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Models.Story Story { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(Guid id)
        {
            var story = await _context.Stories.FirstOrDefaultAsync(m => m.Id == id);

            if (story == null)
            {
                return NotFound();
            }

            Story = story;
            return Page();
        }

        public async Task<IActionResult> OnPostUpdateStoryAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            var storyToUpdate = await _context.Stories.FindAsync(Story.Id);
            if (storyToUpdate == null)
            {
                return NotFound();
            }

            storyToUpdate.Title = Story.Title;
            storyToUpdate.Description = Story.Description;
            storyToUpdate.IsCompleted = Story.IsCompleted;
            storyToUpdate.UpdatedAt = DateTime.UtcNow;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!StoryExists(Story.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return RedirectToPage("./Edit", new { id = Story.Id });
        }

        public async Task<IActionResult> OnPostUpdateContentAsync()
        {
            var storyToUpdate = await _context.Stories.FindAsync(Story.Id);
            if (storyToUpdate == null)
            {
                return NotFound();
            }

            storyToUpdate.Content = Story.Content;
            storyToUpdate.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return RedirectToPage("./Edit", new { id = Story.Id });
        }

        private bool StoryExists(Guid id)
        {
            return _context.Stories.Any(e => e.Id == id);
        }
    }
}
