using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using AICreator.Data;
using AICreator.Models;

namespace AICreator.Pages.Story
{
    public class EditModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public EditModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Models.Story Story { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(Guid id)
        {
            var story = await _context.Stories.FirstOrDefaultAsync(m => m.Id == id);

            if (story == null)
            {
                return NotFound();
            }

            Story = story;
            return Page();
        }

        public async Task<IActionResult> OnPostSaveStep1Async()
        {
            var storyToUpdate = await _context.Stories.FindAsync(Story.Id);
            if (storyToUpdate == null)
            {
                return NotFound();
            }

            storyToUpdate.Genre = Story.Genre;
            storyToUpdate.Characters = Story.Characters;
            storyToUpdate.PlotOutline = Story.PlotOutline;
            storyToUpdate.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            // TODO: Przekierowanie do kroku 2
            return RedirectToPage("./Edit", new { id = Story.Id });
        }

        private bool StoryExists(Guid id)
        {
            return _context.Stories.Any(e => e.Id == id);
        }
    }
}
