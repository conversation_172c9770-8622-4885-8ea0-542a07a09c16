using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using AICreator.Data;
using AICreator.Models;
using AICreator.Services;

namespace AICreator.Pages.Story
{
    public class EditModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly OpenRouterService _openRouterService;

        public EditModel(ApplicationDbContext context, OpenRouterService openRouterService)
        {
            _context = context;
            _openRouterService = openRouterService;
        }

        [BindProperty]
        public Models.Story Story { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(Guid id)
        {
            var story = await _context.Stories.FirstOrDefaultAsync(m => m.Id == id);

            if (story == null)
            {
                return NotFound();
            }

            Story = story;
            return Page();
        }

        public async Task<IActionResult> OnPostSaveStep1Async()
        {
            var storyToUpdate = await _context.Stories.FindAsync(Story.Id);
            if (storyToUpdate == null)
            {
                return NotFound();
            }

            storyToUpdate.Genre = Story.Genre;
            storyToUpdate.Characters = Story.Characters;
            storyToUpdate.PlotOutline = Story.PlotOutline;
            storyToUpdate.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            // Generuj konspekt za pomocą OpenRouter
            try
            {
                var outline = await _openRouterService.GenerateStoryOutlineAsync(
                    Story.Genre ?? "",
                    Story.Characters ?? "",
                    Story.PlotOutline ?? "");

                storyToUpdate.GeneratedOutline = outline;
                await _context.SaveChangesAsync();

                return RedirectToPage("./Step2", new { id = Story.Id });
            }
            catch (Exception ex)
            {
                // W przypadku błędu, zapisz informację i przekieruj do kroku 2
                storyToUpdate.GeneratedOutline = $"Błąd generowania konspektu: {ex.Message}";
                await _context.SaveChangesAsync();
                return RedirectToPage("./Step2", new { id = Story.Id });
            }
        }

        private bool StoryExists(Guid id)
        {
            return _context.Stories.Any(e => e.Id == id);
        }
    }
}
