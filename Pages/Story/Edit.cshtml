@page "{id:guid}"
@model EditModel
@{
    ViewData["Title"] = "Edit Story";
}

<div class="container-fluid h-100">
    <div class="row h-100">
        <!-- Sidebar -->
        <div class="col-md-3 bg-light border-end p-0">
            <div class="story-sidebar">
                <div class="sidebar-header p-3 border-bottom">
                    <h5 class="mb-0">Story Details</h5>
                </div>
                <div class="sidebar-content p-3">
                    <form method="post" asp-page-handler="UpdateStory">
                        <input type="hidden" asp-for="Story.Id" />
                        
                        <div class="mb-3">
                            <label asp-for="Story.Title" class="form-label">Title</label>
                            <input asp-for="Story.Title" class="form-control" />
                            <span asp-validation-for="Story.Title" class="text-danger"></span>
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="Story.Description" class="form-label">Description</label>
                            <textarea asp-for="Story.Description" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Story.Description" class="text-danger"></span>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input asp-for="Story.IsCompleted" class="form-check-input" type="checkbox" />
                                <label asp-for="Story.IsCompleted" class="form-check-label">
                                    Mark as completed
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Save Details</button>
                            <a href="/" class="btn btn-outline-secondary">Back to Home</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Main Content Area -->
        <div class="col-md-9 p-0">
            <div class="story-editor h-100">
                <div class="editor-header p-3 border-bottom bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">@Model.Story.Title</h4>
                        <div class="editor-actions">
                            <button type="button" class="btn btn-success" onclick="saveContent()">
                                <i class="fas fa-save"></i> Save Content
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="editor-content p-3 h-100">
                    <form method="post" asp-page-handler="UpdateContent" id="contentForm">
                        <input type="hidden" asp-for="Story.Id" />
                        <textarea asp-for="Story.Content" class="form-control story-content-editor" 
                                  placeholder="Start writing your story here..."></textarea>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function saveContent() {
            document.getElementById('contentForm').submit();
        }
        
        // Auto-save functionality (optional)
        let autoSaveTimer;
        const contentTextarea = document.querySelector('.story-content-editor');
        
        if (contentTextarea) {
            contentTextarea.addEventListener('input', function() {
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(function() {
                    // Auto-save after 3 seconds of inactivity
                    saveContent();
                }, 3000);
            });
        }
    </script>
}
