@page "{id:guid}"
@model EditModel
@{
    ViewData["Title"] = "Story Creator - Step 1";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <span class="badge bg-light text-primary me-2">Krok 1</span>
                        Podstawowe informacje o historii
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form method="post" asp-page-handler="SaveStep1">
                        <input type="hidden" asp-for="Story.Id" />

                        <div class="mb-4">
                            <label asp-for="Story.Genre" class="form-label fw-bold">
                                <i class="fas fa-book me-2"></i>Gatunek
                            </label>
                            <input asp-for="Story.Genre" class="form-control form-control-lg"
                                   placeholder="np. fantasy, sci-fi, romans, thriller..." />
                            <span asp-validation-for="Story.Genre" class="text-danger"></span>
                        </div>

                        <div class="mb-4">
                            <label asp-for="Story.Characters" class="form-label fw-bold">
                                <i class="fas fa-users me-2"></i>Bohaterowie
                            </label>
                            <textarea asp-for="Story.Characters" class="form-control" rows="4"
                                      placeholder="Opisz głównych bohaterów twojej historii..."></textarea>
                            <span asp-validation-for="Story.Characters" class="text-danger"></span>
                        </div>

                        <div class="mb-4">
                            <label asp-for="Story.PlotOutline" class="form-label fw-bold">
                                <i class="fas fa-map me-2"></i>Ogólny zarys historii
                            </label>
                            <textarea asp-for="Story.PlotOutline" class="form-control" rows="6"
                                      placeholder="Opisz główną fabułę, konflikt i przebieg wydarzeń..."></textarea>
                            <span asp-validation-for="Story.PlotOutline" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="/" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Powrót
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                Dalej <i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
