@page "{id:guid}"
@model Step2Model
@{
    ViewData["Title"] = "Story Creator - Step 2";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <span class="badge bg-light text-success me-2">Krok 2</span>
                        Wygenerowany konspekt opowiadania
                    </h4>
                </div>
                <div class="card-body p-4">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="fas fa-book me-2"></i>Gatunek</h6>
                                    <p class="card-text">@Model.Story.Genre</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="fas fa-users me-2"></i>Bohaterowie</h6>
                                    <p class="card-text">@Model.Story.Characters</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="fas fa-map me-2"></i>Zarys</h6>
                                    <p class="card-text">@Model.Story.PlotOutline</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5 class="mb-3">
                            <i class="fas fa-list-ol me-2"></i>Konspekt opowiadania
                        </h5>
                        <div class="outline-container">
                            @if (!string.IsNullOrEmpty(Model.Story.GeneratedOutline))
                            {
                                <div class="generated-outline" id="outline-editor">
                                    <!-- Przycisk dodawania na początku -->
                                    <div class="add-item-section add-item-top">
                                        <button type="button" class="btn btn-outline-success btn-sm add-between-btn"
                                                onclick="addNewItemBetween(0)" title="Dodaj punkt na początku">
                                            <i class="fas fa-plus me-1"></i>Dodaj punkt na początku
                                        </button>
                                    </div>

                                    @{
                                        var lines = Model.Story.GeneratedOutline.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                                        for (int i = 0; i < lines.Length; i++)
                                        {
                                            <div class="outline-item-container" data-index="@i">
                                                <div class="outline-item-wrapper">
                                                    <div class="outline-item-controls">
                                                        <button type="button" class="btn btn-sm btn-outline-primary edit-item-btn"
                                                                onclick="editItem(@i)" title="Edytuj punkt">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-danger delete-item-btn"
                                                                onclick="deleteItem(@i)" title="Usuń punkt">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                    <div class="outline-item-content">
                                                        <span class="item-number">@(i + 1).</span>
                                                        <div class="outline-item-text" data-index="@i">@lines[i].Trim()</div>
                                                        <textarea class="outline-item-edit hidden"
                                                                  data-index="@i"
                                                                  onblur="saveItemEdit(@i)"
                                                                  onkeydown="handleEditKeyDown(event, @i)">@lines[i].Trim()</textarea>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Przycisk dodawania między punktami -->
                                            <div class="add-item-section add-item-between">
                                                <button type="button" class="btn btn-outline-success btn-sm add-between-btn"
                                                        onclick="addNewItemBetween(@(i + 1))" title="Dodaj punkt tutaj">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        }
                                    }
                                </div>
                            }
                            else
                            {
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Konspekt nie został jeszcze wygenerowany.
                                </div>
                            }
                        </div>
                    </div>

                    <form method="post" asp-page-handler="RegenerateOutline">
                        <input type="hidden" asp-for="Story.Id" />
                        @Html.AntiForgeryToken()
                        <div class="d-flex justify-content-between">
                            <a href="/Story/Edit/@Model.Story.Id" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Wróć do kroku 1
                            </a>
                            <div>
                                <button type="submit" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-sync-alt me-2"></i>Wygeneruj ponownie
                                </button>
                                <button type="button" class="btn btn-success btn-lg" onclick="proceedToStep3()">
                                    Dalej <i class="fas fa-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let storyId = '@Model.Story.Id';

        function proceedToStep3() {
            // TODO: Implementacja przejścia do kroku 3
            alert('Krok 3 będzie wkrótce dostępny!');
        }

        function editItem(index) {
            const container = document.querySelector(`[data-index="${index}"]`).closest('.outline-item-container');
            const textDiv = container.querySelector('.outline-item-text');
            const textarea = container.querySelector('.outline-item-edit');
            const editBtn = container.querySelector('.edit-item-btn');

            // Przełącz na tryb edycji
            textDiv.classList.add('hidden');
            textarea.classList.remove('hidden');
            textarea.focus();

            // Zmień ikonę przycisku
            editBtn.innerHTML = '<i class="fas fa-save"></i>';
            editBtn.title = 'Zapisz zmiany';
            editBtn.onclick = () => saveItemEdit(index);

            // Auto-resize textarea
            autoResize(textarea);
        }

        async function saveItemEdit(index) {
            const container = document.querySelector(`[data-index="${index}"]`).closest('.outline-item-container');
            const textDiv = container.querySelector('.outline-item-text');
            const textarea = container.querySelector('.outline-item-edit');
            const editBtn = container.querySelector('.edit-item-btn');
            const content = textarea.value.trim();

            if (!content) {
                alert('Treść punktu nie może być pusta!');
                return;
            }

            try {
                const response = await fetch('/Story/Step2/' + storyId + '?handler=UpdateItem', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({
                        index: index,
                        content: content
                    })
                });

                if (response.ok) {
                    // Aktualizuj tekst i wróć do trybu wyświetlania
                    textDiv.textContent = content;
                    textDiv.classList.remove('hidden');
                    textarea.classList.add('hidden');

                    // Przywróć przycisk edycji
                    editBtn.innerHTML = '<i class="fas fa-edit"></i>';
                    editBtn.title = 'Edytuj punkt';
                    editBtn.onclick = () => editItem(index);
                } else {
                    console.error('Błąd zapisywania:', response.statusText);
                    alert('Błąd podczas zapisywania zmian!');
                }
            } catch (error) {
                console.error('Błąd zapisywania:', error);
                alert('Błąd podczas zapisywania zmian!');
            }
        }

        async function addNewItemBetween(index) {
            // Utwórz inline edytor dla nowego punktu
            const newItemHtml = `
                <div class="outline-item-container new-item-editing" data-index="new">
                    <div class="outline-item-wrapper">
                        <div class="outline-item-controls">
                            <button type="button" class="btn btn-sm btn-outline-success"
                                    onclick="saveNewItem(${index}, this)" title="Zapisz nowy punkt">
                                <i class="fas fa-save"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary"
                                    onclick="cancelNewItem(this)" title="Anuluj">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="outline-item-content">
                            <span class="item-number">●</span>
                            <textarea class="outline-item-edit new-item-textarea"
                                      placeholder="Wprowadź treść nowego punktu..."
                                      onkeydown="handleNewItemKeyDown(event, ${index}, this)"></textarea>
                        </div>
                    </div>
                </div>
            `;

            // Znajdź miejsce wstawienia
            const addButton = event.target.closest('.add-item-section');
            addButton.insertAdjacentHTML('afterend', newItemHtml);

            // Ukryj przycisk dodawania
            addButton.style.display = 'none';

            // Fokus na textarea
            const newTextarea = document.querySelector('.new-item-textarea');
            newTextarea.focus();
            autoResize(newTextarea);
        }

        async function saveNewItem(index, button) {
            const container = button.closest('.outline-item-container');
            const textarea = container.querySelector('.new-item-textarea');
            const content = textarea.value.trim();

            if (!content) {
                alert('Treść punktu nie może być pusta!');
                return;
            }

            try {
                const response = await fetch('/Story/Step2/' + storyId + '?handler=AddItem', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({
                        index: index,
                        content: content
                    })
                });

                if (response.ok) {
                    location.reload();
                } else {
                    console.error('Błąd dodawania:', response.statusText);
                    alert('Błąd podczas dodawania punktu!');
                }
            } catch (error) {
                console.error('Błąd dodawania:', error);
                alert('Błąd podczas dodawania punktu!');
            }
        }

        function cancelNewItem(button) {
            const container = button.closest('.outline-item-container');
            const addSection = container.previousElementSibling;

            // Pokaż z powrotem przycisk dodawania
            if (addSection && addSection.classList.contains('add-item-section')) {
                addSection.style.display = 'block';
            }

            // Usuń kontener nowego elementu
            container.remove();
        }

        async function deleteItem(index) {
            if (!confirm('Czy na pewno chcesz usunąć ten punkt?')) return;

            try {
                const response = await fetch('/Story/Step2/' + storyId + '?handler=DeleteItem', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({
                        index: index
                    })
                });

                if (response.ok) {
                    location.reload();
                } else {
                    console.error('Błąd usuwania:', response.statusText);
                }
            } catch (error) {
                console.error('Błąd usuwania:', error);
            }
        }

        function handleEditKeyDown(event, index) {
            if (event.key === 'Enter' && event.ctrlKey) {
                event.preventDefault();
                saveItemEdit(index);
            } else if (event.key === 'Escape') {
                event.preventDefault();
                cancelEdit(index);
            }
        }

        function handleNewItemKeyDown(event, index, textarea) {
            if (event.key === 'Enter' && event.ctrlKey) {
                event.preventDefault();
                saveNewItem(index, textarea.closest('.outline-item-container').querySelector('.btn-outline-success'));
            } else if (event.key === 'Escape') {
                event.preventDefault();
                cancelNewItem(textarea.closest('.outline-item-container').querySelector('.btn-outline-secondary'));
            }
        }

        function cancelEdit(index) {
            const container = document.querySelector(`[data-index="${index}"]`).closest('.outline-item-container');
            const textDiv = container.querySelector('.outline-item-text');
            const textarea = container.querySelector('.outline-item-edit');
            const editBtn = container.querySelector('.edit-item-btn');

            // Wróć do trybu wyświetlania bez zapisywania
            textDiv.classList.remove('hidden');
            textarea.classList.add('hidden');

            // Przywróć oryginalną treść
            textarea.value = textDiv.textContent;

            // Przywróć przycisk edycji
            editBtn.innerHTML = '<i class="fas fa-edit"></i>';
            editBtn.title = 'Edytuj punkt';
            editBtn.onclick = () => editItem(index);
        }

        // Auto-resize textareas
        document.addEventListener('DOMContentLoaded', function() {
            const textareas = document.querySelectorAll('.outline-item-edit, .new-item-textarea');
            textareas.forEach(textarea => {
                autoResize(textarea);
                textarea.addEventListener('input', () => autoResize(textarea));
            });
        });

        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.max(textarea.scrollHeight, 60) + 'px';
        }
    </script>
}
