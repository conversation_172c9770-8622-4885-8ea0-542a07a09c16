@page "{id:guid}"
@model Step2Model
@{
    ViewData["Title"] = "Story Creator - Step 2";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <span class="badge bg-light text-success me-2">Krok 2</span>
                        Wygenerowany konspekt opowiadania
                    </h4>
                </div>
                <div class="card-body p-4">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="fas fa-book me-2"></i>Gatunek</h6>
                                    <p class="card-text">@Model.Story.Genre</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="fas fa-users me-2"></i>Bohaterowie</h6>
                                    <p class="card-text">@Model.Story.Characters</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="fas fa-map me-2"></i>Zarys</h6>
                                    <p class="card-text">@Model.Story.PlotOutline</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5 class="mb-3">
                            <i class="fas fa-list-ol me-2"></i>Konspekt opowiadania
                        </h5>
                        <div class="outline-container">
                            @if (!string.IsNullOrEmpty(Model.Story.GeneratedOutline))
                            {
                                <div class="generated-outline" id="outline-editor">
                                    @{
                                        var lines = Model.Story.GeneratedOutline.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                                        for (int i = 0; i < lines.Length; i++)
                                        {
                                            <div class="outline-item-container" data-index="@i">
                                                <div class="outline-item-wrapper">
                                                    <div class="outline-item-controls">
                                                        <button type="button" class="btn btn-sm btn-outline-success add-item-btn"
                                                                onclick="addNewItem(@i)" title="Dodaj punkt powyżej">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-danger delete-item-btn"
                                                                onclick="deleteItem(@i)" title="Usuń punkt">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                    <div class="outline-item-content">
                                                        <span class="item-number">@(i + 1).</span>
                                                        <textarea class="outline-item-text"
                                                                  data-index="@i"
                                                                  onblur="saveItem(@i, this.value)"
                                                                  onkeydown="handleKeyDown(event, @i)">@lines[i].Trim()</textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    }
                                    <div class="add-item-bottom">
                                        <button type="button" class="btn btn-outline-success"
                                                onclick="addNewItem(@lines.Length)">
                                            <i class="fas fa-plus me-2"></i>Dodaj nowy punkt
                                        </button>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Konspekt nie został jeszcze wygenerowany.
                                </div>
                            }
                        </div>
                    </div>

                    <form method="post" asp-page-handler="RegenerateOutline">
                        <input type="hidden" asp-for="Story.Id" />
                        @Html.AntiForgeryToken()
                        <div class="d-flex justify-content-between">
                            <a href="/Story/Edit/@Model.Story.Id" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Wróć do kroku 1
                            </a>
                            <div>
                                <button type="submit" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-sync-alt me-2"></i>Wygeneruj ponownie
                                </button>
                                <button type="button" class="btn btn-success btn-lg" onclick="proceedToStep3()">
                                    Dalej <i class="fas fa-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let storyId = '@Model.Story.Id';

        function proceedToStep3() {
            // TODO: Implementacja przejścia do kroku 3
            alert('Krok 3 będzie wkrótce dostępny!');
        }

        async function saveItem(index, content) {
            if (!content.trim()) return;

            try {
                const response = await fetch('/Story/Step2/' + storyId + '?handler=UpdateItem', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({
                        index: index,
                        content: content.trim()
                    })
                });

                if (!response.ok) {
                    console.error('Błąd zapisywania:', response.statusText);
                }
            } catch (error) {
                console.error('Błąd zapisywania:', error);
            }
        }

        async function addNewItem(index) {
            const newContent = prompt('Wprowadź treść nowego punktu:');
            if (!newContent || !newContent.trim()) return;

            try {
                const response = await fetch('/Story/Step2/' + storyId + '?handler=AddItem', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({
                        index: index,
                        content: newContent.trim()
                    })
                });

                if (response.ok) {
                    location.reload();
                } else {
                    console.error('Błąd dodawania:', response.statusText);
                }
            } catch (error) {
                console.error('Błąd dodawania:', error);
            }
        }

        async function deleteItem(index) {
            if (!confirm('Czy na pewno chcesz usunąć ten punkt?')) return;

            try {
                const response = await fetch('/Story/Step2/' + storyId + '?handler=DeleteItem', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({
                        index: index
                    })
                });

                if (response.ok) {
                    location.reload();
                } else {
                    console.error('Błąd usuwania:', response.statusText);
                }
            } catch (error) {
                console.error('Błąd usuwania:', error);
            }
        }

        function handleKeyDown(event, index) {
            if (event.key === 'Enter' && event.ctrlKey) {
                event.preventDefault();
                addNewItem(index + 1);
            }
        }

        // Auto-resize textareas
        document.addEventListener('DOMContentLoaded', function() {
            const textareas = document.querySelectorAll('.outline-item-text');
            textareas.forEach(textarea => {
                autoResize(textarea);
                textarea.addEventListener('input', () => autoResize(textarea));
            });
        });

        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
        }
    </script>
}
