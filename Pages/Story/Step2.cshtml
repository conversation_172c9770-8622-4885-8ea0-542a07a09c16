@page "{id:guid}"
@model Step2Model
@{
    ViewData["Title"] = "Story Creator - Step 2";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <span class="badge bg-light text-success me-2">Krok 2</span>
                        Wygenerowany konspekt opowiadania
                    </h4>
                </div>
                <div class="card-body p-4">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="fas fa-book me-2"></i>Gatunek</h6>
                                    <p class="card-text">@Model.Story.Genre</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="fas fa-users me-2"></i>Bohaterowie</h6>
                                    <p class="card-text">@Model.Story.Characters</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="fas fa-map me-2"></i>Zarys</h6>
                                    <p class="card-text">@Model.Story.PlotOutline</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5 class="mb-3">
                            <i class="fas fa-list-ol me-2"></i>Konspekt opowiadania
                        </h5>
                        <div class="outline-container">
                            @if (!string.IsNullOrEmpty(Model.Story.GeneratedOutline))
                            {
                                <div class="generated-outline">
                                    @foreach (var line in Model.Story.GeneratedOutline.Split('\n', StringSplitOptions.RemoveEmptyEntries))
                                    {
                                        <div class="outline-item">
                                            @line.Trim()
                                        </div>
                                    }
                                </div>
                            }
                            else
                            {
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Konspekt nie został jeszcze wygenerowany.
                                </div>
                            }
                        </div>
                    </div>

                    <form method="post" asp-page-handler="RegenerateOutline">
                        <input type="hidden" asp-for="Story.Id" />
                        <div class="d-flex justify-content-between">
                            <a href="/Story/Edit/@Model.Story.Id" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Wróć do kroku 1
                            </a>
                            <div>
                                <button type="submit" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-sync-alt me-2"></i>Wygeneruj ponownie
                                </button>
                                <button type="button" class="btn btn-success btn-lg" onclick="proceedToStep3()">
                                    Dalej <i class="fas fa-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function proceedToStep3() {
            // TODO: Implementacja przejścia do kroku 3
            alert('Krok 3 będzie wkrótce dostępny!');
        }
    </script>
}
