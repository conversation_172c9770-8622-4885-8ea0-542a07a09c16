using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using AICreator.Data;
using AICreator.Models;

namespace AICreator.Pages;

public class IndexModel : PageModel
{
    private readonly ILogger<IndexModel> _logger;
    private readonly ApplicationDbContext _context;

    public IndexModel(ILogger<IndexModel> logger, ApplicationDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public void OnGet()
    {

    }

    public async Task<IActionResult> OnPostCreateStoryAsync()
    {
        var story = new Models.Story
        {
            Id = Guid.NewGuid(),
            Title = "Untitled Story",
            Content = "",
            Description = "",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsCompleted = false
        };

        _context.Stories.Add(story);
        await _context.SaveChangesAsync();

        return RedirectToPage("/Story/Edit", new { id = story.Id });
    }
}
