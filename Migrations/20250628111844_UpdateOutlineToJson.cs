using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AICreator.Migrations
{
    /// <inheritdoc />
    public partial class UpdateOutlineToJson : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Najpierw dodaj nową kolumnę
            migrationBuilder.AddColumn<string>(
                name: "GeneratedOutlineJson",
                table: "Stories",
                type: "TEXT",
                nullable: true);

            // Migruj dane z GeneratedOutline do GeneratedOutlineJson
            migrationBuilder.Sql(@"
                UPDATE Stories
                SET GeneratedOutlineJson = CASE
                    WHEN GeneratedOutline IS NULL OR GeneratedOutline = '' THEN '[]'
                    ELSE '[' || REPLACE(REPLACE(GeneratedOutline, CHAR(10), '"",'""'), '"",'""', '"",""') || ']'
                END
                WHERE GeneratedOutline IS NOT NULL
            ");

            // Usuń starą kolumnę
            migrationBuilder.DropColumn(
                name: "GeneratedOutline",
                table: "Stories");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "GeneratedOutlineJson",
                table: "Stories",
                newName: "GeneratedOutline");
        }
    }
}
