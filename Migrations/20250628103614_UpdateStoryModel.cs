﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AICreator.Migrations
{
    /// <inheritdoc />
    public partial class UpdateStoryModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Description",
                table: "Stories");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "Stories");

            migrationBuilder.AddColumn<string>(
                name: "Characters",
                table: "Stories",
                type: "TEXT",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Genre",
                table: "Stories",
                type: "TEXT",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PlotOutline",
                table: "Stories",
                type: "TEXT",
                maxLength: 2000,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Characters",
                table: "Stories");

            migrationBuilder.DropColumn(
                name: "Genre",
                table: "Stories");

            migrationBuilder.DropColumn(
                name: "PlotOutline",
                table: "Stories");

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "Stories",
                type: "TEXT",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "Stories",
                type: "TEXT",
                maxLength: 200,
                nullable: false,
                defaultValue: "");
        }
    }
}
